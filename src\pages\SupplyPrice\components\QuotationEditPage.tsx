import React, { useState, useEffect } from "react";
import {
  Button,
  Typography,
  Card,
  Space,
  message,
  Row,
  Col,
  Tag,
  Badge,
  Form,
  Input,
  InputNumber,
  Statistic,
  Tooltip,
} from "antd";
import {
  ArrowLeftOutlined,
  CopyOutlined,
  FileTextOutlined,
  EnvironmentOutlined,
  RocketOutlined,
  BoxPlotOutlined,
  DollarCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  CalculatorOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { formatDepartureDates, copyToClipboard } from "@/utils/util";

const { Title, Text } = Typography;
const { TextArea } = Input;

// 价格字段常量
const PRICE_FIELDS = {
  pickingfee: "pickingfee",
  brandfee: "brandfee",
  afreightprice: "afreightprice",
  smallcargofee: "smallcargofee",
} as const;

// 价格调整表单字段配置
const PRICE_ADJUSTMENT_FIELDS = [
  { key: "afreightprice", labelKey: undefined, label: "A/F单价" },
  {
    key: "pickingfee",
    labelKey: "quotationEditor.fields.pickingFee",
    label: undefined,
  },
  { key: "brandfee", labelKey: undefined, label: "品牌费用" },
  {
    key: "smallcargofee",
    labelKey: "quotationEditor.fields.smallCargoFee",
    label: undefined,
  },
] as const;

const BULK_PRICE_CONFIG = [
  { key: "q300price", label: "Q300", color: "#722ed1" },
  { key: "q500price", label: "Q500", color: "#fa8c16" },
  { key: "q1000price", label: "Q1000", color: "#eb2f96" },
] as const;

const BULK_PRICE_ITEM_STYLE = {
  padding: "8px 12px",
  backgroundColor: "#fafafa",
  borderRadius: "6px",
  border: "1px solid #f0f0f0",
} as const;

const BULK_PRICE_VALUE_STYLE = {
  marginLeft: "8px",
  fontWeight: "bold",
  color: "#262626",
} as const;

const getInitialPriceText = (tradeterms: string) => {
  if (tradeterms === "FOB") {
    return "";
  } else if (tradeterms === "FCA") {
    return `Terminal charge      :0.1USD/KG;Min 10USD(Charge by CW)
Handle fee           :50USD/shpt
Air doc charge       :10USD/shpt
ENS_charge           :10USD/shpt`;
  } else {
    // EXW 或其他情况
    return `Terminal charge      :0.1USD/KG;Min 10USD(Charge by CW)
Handle fee           :50USD/shpt
Customs              :50USD/shpt (up to 6 items only, thereafter USD15 per item)
Air doc charge       :10USD/shpt
ENS_charge           :10USD/shpt`;
  }
};

interface QuotationEditPageProps {
  onBack: () => void;
  supplyPriceId: string | null;
  priceData: any;
  tradeterms?: string;
}

const QuotationEditPage: React.FC<QuotationEditPageProps> = ({
  onBack,
  supplyPriceId,
  priceData,
  tradeterms,
}) => {
  const [form] = Form.useForm();
  const [priceForm] = Form.useForm();
  const [currentPrices, setCurrentPrices] = useState<any>({});
  const [calculatedTotal, setCalculatedTotal] = useState<number>(0);
  const [tempPrices, setTempPrices] = useState<any>({});
  const [hasChanges, setHasChanges] = useState(false);
  const { t } = useTranslation();
  const createInitialPrices = (data: any) => {
    return Object.keys(PRICE_FIELDS).reduce((acc, key) => {
      acc[key] = data?.[key] || 0;
      return acc;
    }, {} as any);
  };

  useEffect(() => {
    if (priceData) {
      // 使用选中日期的价格数据，如果没有则使用原始数据
      const selectedTotalPrice = priceData?.selectedTotalPrice || 0;
      const selectedAfreightPrice = priceData?.selectedAfreightPrice || 0;

      // 创建包含选中日期价格的初始价格对象
      const initialPrices = {
        ...createInitialPrices(priceData),
        afreightprice: selectedAfreightPrice,
      };

      setCurrentPrices(initialPrices);
      setTempPrices(initialPrices);
      setCalculatedTotal(selectedTotalPrice);
      priceForm.setFieldsValue(initialPrices);
      setHasChanges(false);

      generateQuotationContent(initialPrices);
    }
  }, [priceData]);

  const generateQuotationContent = (prices: any) => {
    const startIndex = priceData?.afprice?.indexOf("CNY");
    const unit = priceData?.afprice?.slice(startIndex);

    // 使用选中日期的时效，如果没有则使用原始时效
    const selectedValidity = priceData?.selectedValidity || 0;

    const currentTradeterms = tradeterms || "EXW";
    const initialPriceText = getInitialPriceText(currentTradeterms);
    const localChargeSection = initialPriceText
      ? `${currentTradeterms} Local charge:
${initialPriceText}`
      : `${currentTradeterms} Local charge:`;

    const formattedContent = `Airline              :${priceData?.airlinename}
Route                :${
      priceData?.istransfer
        ? `${priceData?.originport}-${priceData?.transfer}-${priceData?.unloadingport}`
        : `${priceData?.originport}-${priceData?.unloadingport}`
    }
T/T                  :${selectedValidity}${selectedValidity && selectedValidity > 1 ? "days" : "day"}
Schedule             :${
      priceData?.istransfer
        ? `${priceData?.originschedules}-${priceData?.transferschedules}`
        : `${priceData?.originschedules}`
    }
Departure date       :${formatDepartureDates(priceData?.optionaldate)}
A/F                  :${prices?.afreightprice?.toFixed(1)}${unit}
CW                   :${priceData?.cwprice}kgs

${localChargeSection}
Pick up time         :${
      priceData?.domesticvalidity
        ? `${priceData?.domesticvalidity}${priceData?.domesticvalidity && priceData?.domesticvalidity > 1 ? "days" : "day"}`
        : t("quotationEditor.additionalFees.none")
    }
Pick up fee          :${prices.pickingfee?.toFixed(1) || 0} CNY
Brand_cost           :${prices.brandfee?.toFixed(1) || 0} CNY
Car_paking           :${priceData.carpaking || "无"}
Smallcargo_fee       :${prices.smallcargofee?.toFixed(1) || 0} CNY`;

    form.setFieldsValue({ quotationContent: formattedContent });
  };

  const handlePriceChange = (field: string, value: number) => {
    const newTempPrices = { ...tempPrices, [field]: value || 0 };
    setTempPrices(newTempPrices);
    setHasChanges(true);
  };
  const handleConfirmPriceAdjustment = () => {
    setCurrentPrices(tempPrices);

    const newTotal = calculateTotal(tempPrices);
    setCalculatedTotal(newTotal);

    generateQuotationContent(tempPrices);

    setHasChanges(false);
    message.success(t("quotationEditor.messages.priceAdjustmentConfirmed"));
  };

  const handleCancelPriceAdjustment = () => {
    setTempPrices(currentPrices);
    priceForm.setFieldsValue(currentPrices);
    setHasChanges(false);
    message.info(t("quotationEditor.messages.priceAdjustmentCanceled"));
  };

  const calculateTotal = (prices: any) => {
    // 使用选中日期的总价作为基础价格，如果没有则使用原始总价
    const basePrice = priceData?.selectedTotalPrice || 0;

    // 使用选中日期的A/F价格作为原始A/F价格进行比较
    const originalAfreightPrice = priceData?.selectedAfreightPrice || 0;

    // 计算各项费用的实际变化量（新价格 - 原始价格）
    let afreightPrice = 0;
    if (priceData?.useprice > 0) {
      afreightPrice =
        ((prices.afreightprice || 0) - originalAfreightPrice) *
        priceData?.cwprice;
    } else {
      afreightPrice = (prices.afreightprice || 0) - originalAfreightPrice;
    }
    const pickingfeeChange =
      (prices.pickingfee || 0) - (priceData?.pickingfee || 0);
    const brandfeeChange = (prices.brandfee || 0) - (priceData?.brandfee || 0);
    const smallcargofeeChange =
      (prices.smallcargofee || 0) - (priceData?.smallcargofee || 0);

    const priceChanges =
      afreightPrice + pickingfeeChange + brandfeeChange + smallcargofeeChange;

    return basePrice + priceChanges;
  };

  const handleResetPrices = () => {
    const originalPrices = createInitialPrices(priceData);

    setCurrentPrices(originalPrices);
    setTempPrices(originalPrices);
    setCalculatedTotal(priceData?.totalprice || 0);
    priceForm.setFieldsValue(originalPrices);
    generateQuotationContent(originalPrices);
    setHasChanges(false);
    message.success(t("quotationEditor.messages.resetSuccess"));
  };

  const handleCopy = async () => {
    const content = form.getFieldValue("quotationContent");
    if (!content) return;

    // 构造带等宽字体的 HTML
    const html = `<pre style="font-family:Consolas, 'Courier New', monospace; font-size:14px; margin:0;">${content
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")}</pre>`;

    try {
      // 优先尝试富文本复制（支持HTML格式）
      if (navigator.clipboard && window.ClipboardItem) {
        const blobHtml = new Blob([html], { type: "text/html" });
        const blobText = new Blob([content], { type: "text/plain" });
        await navigator.clipboard.write([
          new window.ClipboardItem({
            "text/html": blobHtml,
            "text/plain": blobText,
          }),
        ]);
        message.success("富文本复制成功！粘贴到 Word/邮件时将自动对齐。");
      } else if (navigator.clipboard) {
        // 降级到纯文本复制
        await navigator.clipboard.writeText(content);
        message.info("已复制为纯文本，部分平台可能无法自动对齐。");
      } else {
        // 最后降级到工具函数复制
        const success = await copyToClipboard(content);
        if (success) {
          message.success(t("quotationEditor.messages.copySuccess"));
        } else {
          message.error(t("quotationEditor.messages.copyFailed"));
        }
      }
    } catch (err) {
      // 如果富文本复制失败，尝试纯文本复制
      try {
        const success = await copyToClipboard(content);
        if (success) {
          message.success(t("quotationEditor.messages.copySuccess"));
        } else {
          message.error("复制失败，请手动全选复制。");
        }
      } catch (fallbackErr) {
        message.error("复制失败，请手动全选复制。");
      }
    }
  };

  // 渲染价格输入字段
  const renderPriceInputField = (
    fieldConfig: (typeof PRICE_ADJUSTMENT_FIELDS)[number]
  ) => {
    const label = fieldConfig.labelKey
      ? t(fieldConfig.labelKey)
      : fieldConfig.label;
    return (
      <Col span={12} key={fieldConfig.key}>
        <Form.Item
          label={label}
          name={fieldConfig.key}
          style={{ marginBottom: 8 }}
        >
          <InputNumber
            style={{ width: "100%" }}
            min={0}
            precision={1}
            onChange={(value) => handlePriceChange(fieldConfig.key, value || 0)}
          />
        </Form.Item>
      </Col>
    );
  };

  // 渲染大货价格项
  const renderBulkPriceItem = (config: (typeof BULK_PRICE_CONFIG)[number]) => (
    <Col span={8} key={config.key}>
      <div className="tier-item" style={BULK_PRICE_ITEM_STYLE}>
        <Text strong style={{ color: config.color }}>
          {config.label}:
        </Text>
        <Text className="tier-value" style={BULK_PRICE_VALUE_STYLE}>
          {priceData[config.key] || 0} CNY/KG
        </Text>
      </div>
    </Col>
  );

  const formatBoolean = (value: boolean) => {
    return value ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>
        {t("quotationEditor.booleanValues.yes")}
      </Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>
        {t("quotationEditor.booleanValues.no")}
      </Tag>
    );
  };

  return (
    <div className="quotation-edit-page">
      {/* 顶部导航栏 */}
      <div className="page-header">
        <div className="header-left">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            className="back-button"
          >
            {t("common.back")}
          </Button>
          <div className="header-info">
            <Title level={3} className="page-title">
              <FileTextOutlined /> {t("quotationEditor.title")}
            </Title>
            <div className="header-meta">
              <Space size="large" className="meta-items">
                <div className="meta-item">
                  <Text type="secondary" style={{ fontSize: "12px" }}>
                    {t("quotationEditor.fields.priceId")}
                  </Text>
                  <Tag
                    color="blue"
                    style={{ marginLeft: "4px", fontWeight: "500" }}
                  >
                    {supplyPriceId}
                  </Tag>
                </div>
                <div className="meta-item">
                  <Text type="secondary" style={{ fontSize: "12px" }}>
                    {t("quotationEditor.fields.department")}
                  </Text>
                  <Tag
                    color="green"
                    style={{
                      marginLeft: "4px",
                      fontWeight: "500",
                      borderRadius: "6px",
                      padding: "2px 8px",
                    }}
                  >
                    {priceData?.departname || "-"}
                  </Tag>
                </div>
              </Space>
            </div>
          </div>
        </div>
      </div>

      {/* 总价展示横幅 */}
      <div className="total-price-banner">
        <div className="price-display">
          <div className="price-comparison-group">
            <div className="price-pair">
              <div className="original-price">
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  {t("quotationEditor.fields.originalPrice")}
                </Text>
                <Statistic
                  value={priceData?.selectedTotalPrice || 0}
                  precision={1}
                  prefix="￥"
                  valueStyle={{ color: "#8c8c8c", fontSize: "20px" }}
                />
              </div>
              <div className="price-arrow">
                <ThunderboltOutlined
                  style={{ fontSize: "20px", color: "#1890ff" }}
                />
              </div>
              <div className="calculated-price">
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  {t("quotationEditor.fields.adjustedPrice")}
                </Text>
                <Statistic
                  value={calculatedTotal}
                  precision={1}
                  prefix="￥"
                  valueStyle={{
                    color: "#f5222d",
                    fontSize: "24px",
                    fontWeight: "bold",
                  }}
                />
              </div>
            </div>
          </div>
          <div className="profit-display">
            <div className="profit-info">
              <Text type="secondary" style={{ fontSize: "14px" }}>
                {t("quotationEditor.fields.profit")}
              </Text>
              <div className="profit-value">
                <Statistic
                  value={calculatedTotal - (priceData?.selectedTotalPrice || 0)}
                  precision={1}
                  prefix="￥"
                  valueStyle={{
                    color:
                      calculatedTotal - (priceData?.selectedTotalPrice || 0) >=
                      0
                        ? "#52c41a"
                        : "#ff4d4f",
                    fontSize: "18px",
                    fontWeight: "600",
                  }}
                />
                <Text
                  type="secondary"
                  style={{
                    marginLeft: "8px",
                    fontSize: "12px",
                    color:
                      calculatedTotal - (priceData?.selectedTotalPrice || 0) >=
                      0
                        ? "#52c41a"
                        : "#ff4d4f",
                  }}
                >
                  {calculatedTotal - (priceData?.selectedTotalPrice || 0) >= 0
                    ? "↗"
                    : "↘"}
                  {(
                    ((calculatedTotal - (priceData?.selectedTotalPrice || 0)) /
                      (priceData?.selectedTotalPrice || 1)) *
                    100
                  ).toFixed(1)}
                  %
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="page-content">
        <Row gutter={24}>
          {/* 左侧：报价单内容（主要区域） */}
          <Col span={14}>
            <Card
              className="quotation-content-card"
              title={
                <div className="card-title">
                  <FileTextOutlined />
                  {t("quotationEditor.sections.quotationContent")}
                </div>
              }
              extra={
                <Tooltip title="复制报价内容（支持富文本格式）">
                  <Button
                    type="text"
                    icon={<CopyOutlined />}
                    onClick={handleCopy}
                    size="small"
                  >
                    复制
                  </Button>
                </Tooltip>
              }
            >
              <Form form={form} layout="vertical">
                <Form.Item
                  name="quotationContent"
                  rules={[
                    {
                      required: true,
                      message: t(
                        "quotationEditor.validation.quotationContentRequired"
                      ),
                    },
                  ]}
                >
                  <TextArea
                    className="quotation-textarea"
                    placeholder={t(
                      "quotationEditor.placeholders.quotationContent"
                    )}
                    autoSize={{ minRows: 20, maxRows: 40 }}
                    style={{
                      whiteSpace: "pre-wrap",
                      fontFamily:
                        "JetBrains Mono, Fira Code, Courier New, monospace",
                      fontSize: "14px",
                      lineHeight: "1.6",
                    }}
                  />
                </Form.Item>
              </Form>
            </Card>
          </Col>

          {/* 右侧：价格调整和详情 */}
          <Col span={10}>
            <Space direction="vertical" size="small" style={{ width: "100%" }}>
              {/* 价格调整表单 */}
              <Card
                className="price-adjustment-card"
                title={
                  <div className="card-title">
                    <CalculatorOutlined />
                    {t("quotationEditor.sections.priceAdjustment")}
                  </div>
                }
                extra={
                  <Space size="small">
                    <Button
                      type="default"
                      icon={<ReloadOutlined />}
                      onClick={handleResetPrices}
                      size="small"
                    >
                      {t("quotationEditor.buttons.reset")}
                    </Button>
                  </Space>
                }
              >
                <Form form={priceForm} layout="vertical" size="small">
                  <Row gutter={[12, 4]}>
                    {PRICE_ADJUSTMENT_FIELDS.map(renderPriceInputField)}
                  </Row>

                  {/* 价格调整确认按钮 */}
                  {hasChanges && (
                    <Row
                      style={{
                        marginTop: 12,
                        paddingTop: 12,
                        borderTop: "1px solid #f0f0f0",
                        justifyContent: "flex-end",
                        alignItems: "center",
                      }}
                    >
                      <Space>
                        <Button
                          type="primary"
                          icon={<CheckCircleOutlined />}
                          onClick={handleConfirmPriceAdjustment}
                        >
                          {t("quotationEditor.buttons.confirmAdjustment")}
                        </Button>
                        <Button
                          type="default"
                          icon={<CloseCircleOutlined />}
                          onClick={handleCancelPriceAdjustment}
                        >
                          {t("quotationEditor.buttons.cancelAdjustment")}
                        </Button>
                      </Space>
                    </Row>
                  )}
                </Form>
              </Card>

              {/* 航线信息 */}
              <Card
                className="detail-info-card"
                title={
                  <div className="card-title">
                    <RocketOutlined />
                    {t("quotationEditor.sections.routeInfo")}
                  </div>
                }
                size="small"
              >
                <Row gutter={[8, 8]}>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.airline")}
                      </Text>
                      <div>
                        <Tag color="blue">{priceData?.airlinename || "-"}</Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.originPort")}
                      </Text>
                      <div>
                        <Tag color="green" icon={<EnvironmentOutlined />}>
                          {priceData?.originport || "-"}
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.destinationPort")}
                      </Text>
                      <div>
                        {priceData?.unloadingport
                          ?.split("/")
                          .map((item: string) => (
                            <Tag
                              key={item}
                              color="orange"
                              icon={<EnvironmentOutlined />}
                              style={{ marginBottom: 1, marginRight: 2 }}
                            >
                              {item}
                            </Tag>
                          ))}
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.originSchedules")}
                      </Text>
                      <div>{priceData?.originschedules || "-"}</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.isTransfer")}
                      </Text>
                      <div>{formatBoolean(priceData?.istransfer)}</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.transferPort")}
                      </Text>
                      <div>
                        <Tag color="purple" icon={<EnvironmentOutlined />}>
                          {priceData?.transfer || "-"}
                        </Tag>
                      </div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.transferSchedules")}
                      </Text>
                      <div>{priceData?.transferschedules || "-"}</div>
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 货物限制信息 */}
              <Card
                className="detail-info-card"
                title={
                  <div className="card-title">
                    <BoxPlotOutlined />
                    {t("quotationEditor.sections.cargoLimits")}
                  </div>
                }
                size="small"
              >
                <Row gutter={[8, 8]}>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.minDensity")}
                      </Text>
                      <div>{priceData?.mindensity ?? "-"} kg/m³</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.maxDensity")}
                      </Text>
                      <div>{priceData?.maxdensity ?? "-"} kg/m³</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.lengthLimit")}
                      </Text>
                      <div>{priceData?.lengthlimit ?? "-"}</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.widthLimit")}
                      </Text>
                      <div>{priceData?.widthlimit ?? "-"}</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.heightLimit")}
                      </Text>
                      <div>{priceData?.heightlimit ?? "-"}</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.canEnsureCabin")}
                      </Text>
                      <div>{formatBoolean(priceData?.iscabin)}</div>
                    </div>
                  </Col>
                  {priceData?.iscabin && (
                    <Col span={6}>
                      <div className="info-item">
                        <Text type="secondary">
                          {t("quotationEditor.fields.cabinPrice")}
                        </Text>
                        <div>{priceData?.cabinprice} CNY</div>
                      </div>
                    </Col>
                  )}
                  <Col span={6}>
                    <div className="info-item">
                      <Text type="secondary">
                        {t("quotationEditor.fields.smallCargoFee")}
                      </Text>
                      <div>
                        {priceData?.smallcargofee
                          ? `${priceData?.smallcargofee}CNY`
                          : t("quotationEditor.additionalFees.none")}
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 价格梯度信息 */}
              <Card
                className="detail-info-card"
                title={
                  <div className="card-title">
                    <DollarCircleOutlined />
                    {t("quotationEditor.sections.priceGradient")}
                  </div>
                }
                size="small"
              >
                <Row gutter={[8, 8]}>
                  {/* M价 */}
                  <Col span={12}>
                    <div className="price-tier">
                      <div className="tier-header">
                        <Badge
                          color="#1890ff"
                          text={t("quotationEditor.priceTypes.mPrice")}
                        />
                        <Text type="secondary">
                          {t("quotationEditor.priceTypes.standardPrice")}
                        </Text>
                      </div>
                      <div className="tier-content">
                        <div className="tier-item">
                          <Text>
                            {t("quotationEditor.priceLabels.standard")}:
                          </Text>
                          <Text className="tier-value">
                            {priceData?.mprice || 0} CNY/KG
                          </Text>
                        </div>
                      </div>
                    </div>
                  </Col>
                  {/* N价 */}
                  <Col span={12}>
                    <div className="price-tier">
                      <div className="tier-header">
                        <Badge
                          color="#52c41a"
                          text={t("quotationEditor.priceTypes.nPrice")}
                        />
                        <Text type="secondary">
                          {t("quotationEditor.priceTypes.basePrice")}
                        </Text>
                      </div>
                      <div className="tier-content">
                        <div className="tier-item">
                          <Text>{t("quotationEditor.priceLabels.base")}:</Text>
                          <Text className="tier-value">
                            {priceData?.nprice || 0} CNY/KG
                          </Text>
                        </div>
                      </div>
                    </div>
                  </Col>
                  {/* Q45价 */}
                  <Col span={12}>
                    <div className="price-tier">
                      <div className="tier-header">
                        <Badge
                          color="#722ed1"
                          text={t("quotationEditor.priceTypes.q45Price")}
                        />
                        <Text type="secondary">
                          {t("quotationEditor.priceTypes.above45kg")}
                        </Text>
                      </div>
                      <div className="tier-content">
                        <div className="tier-item">
                          <Text>
                            {t("quotationEditor.priceLabels.standardTier")}:
                          </Text>
                          <Text className="tier-value">
                            {priceData?.q45price || 0} CNY/KG
                          </Text>
                        </div>
                        {priceData?.q45_sub && (
                          <div className="tier-item">
                            <Text>
                              {t("quotationEditor.priceLabels.segment")}:
                            </Text>
                            <Text className="tier-value">
                              {priceData?.subq45price || 0} CNY/KG
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </Col>
                  {/* Q100价 */}
                  <Col span={12}>
                    <div className="price-tier">
                      <div className="tier-header">
                        <Badge
                          color="#fa8c16"
                          text={t("quotationEditor.priceTypes.q100Price")}
                        />
                        <Text type="secondary">
                          {t("quotationEditor.priceTypes.above100kg")}
                        </Text>
                      </div>
                      <div className="tier-content">
                        <div className="tier-item">
                          <Text>
                            {t("quotationEditor.priceLabels.standardTier")}:
                          </Text>
                          <Text className="tier-value">
                            {priceData?.q100price || 0} CNY/KG
                          </Text>
                        </div>
                        {priceData?.q100_sub && (
                          <div className="tier-item">
                            <Text>
                              {t("quotationEditor.priceLabels.segment")}:
                            </Text>
                            <Text className="tier-value">
                              {priceData?.subq100price || 0} CNY/KG
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </Col>
                  {/* 大货价格 */}
                  <Col span={24}>
                    <div className="price-tier">
                      <div className="tier-header">
                        <Badge
                          color="#eb2f96"
                          text={t("quotationEditor.priceTypes.bulkPrice")}
                        />
                        <Text type="secondary">
                          {t("quotationEditor.priceTypes.above300kg")}
                        </Text>
                      </div>
                      <div className="tier-content">
                        <Row gutter={[16, 8]}>
                          {BULK_PRICE_CONFIG.map(renderBulkPriceItem)}
                        </Row>
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>

              {/* 特殊物品附加费 */}
              {Array.isArray(priceData?.specialcharges) &&
                priceData?.specialcharges.length > 0 && (
                  <Card
                    className="detail-info-card"
                    title={
                      <div className="card-title">
                        <BoxPlotOutlined />
                        {t("quotationEditor.sections.specialCharges")}
                      </div>
                    }
                    size="small"
                  >
                    <Row gutter={[8, 8]}>
                      {priceData?.specialcharges.map(
                        (item: any, index: number) => (
                          <Col key={index} span={6}>
                            <div className="charge-item">
                              <Text type="secondary">{item.specialItem}</Text>
                              <Tag color="red">+{item.value}</Tag>
                            </div>
                          </Col>
                        )
                      )}
                    </Row>
                  </Card>
                )}

              {/* 包装费用 */}
              {Array.isArray(priceData?.packagecharges) &&
                priceData?.packagecharges.length > 0 && (
                  <Card
                    className="detail-info-card"
                    title={
                      <div className="card-title">
                        <BoxPlotOutlined />
                        {t("quotationEditor.sections.packageCharges")}
                      </div>
                    }
                    size="small"
                  >
                    <Row gutter={[8, 8]}>
                      {priceData?.packagecharges.map(
                        (item: any, index: number) => (
                          <Col key={index} span={6}>
                            <div className="charge-item">
                              <Text type="secondary">{item.packageItem}</Text>
                              <Tag color="cyan">+{item.value}</Tag>
                            </div>
                          </Col>
                        )
                      )}
                    </Row>
                  </Card>
                )}
            </Space>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default QuotationEditPage;
